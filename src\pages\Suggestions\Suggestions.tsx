import {
    StyledModalActions,
    StyledModalContent,
    StyledModalHeader,
    StyledModalTitle,
    StyledSingleSelect,
    SuggestionProgressId
} from '@common';
import { PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import ListHeader from '@components/reusableComponents/ListHeader';
import { But<PERSON>, Tabs } from '@medly-components/core';
import { CallMadeIcon, CallReceivedIcon } from '@medly-components/icons';
import DOMPurify from 'dompurify';
import { ReceivedCols, SubmittedCols } from './columns';
import {
    ActionsWrapper,
    CommentLimitText,
    CommentTextField,
    FilterWrapper,
    StyledHeading,
    StyledHTMLText,
    StyledModalContentFlex,
    StyledPopup,
    StyledTableWrapper,
    StyledTabs,
    StyledText,
    SuggestionCommentContainer,
    SuggestionCommentsSection,
    SuggestionDescriptionLayer,
    SuggestionLayer
} from './Suggestions.styled';
import { useSuggestion } from './useSuggestion';
import ClearFilter from '@components/reusableComponents/ClearFilter/ClearFilter';

export const Suggestions = () => {
    const {
        modalState,
        suggestedDate,
        suggestedBy,
        suggestionText,
        isReceivedAllowed,
        categoryName,
        // openModal,
        closeModal,
        addSuggestionPage,
        handleTabChange,
        suggestionsData,
        totalSuggestionsDataCount,
        handlePageChange,
        page,
        activeTab,
        suggestionsDataIsLoading,
        handleSortChange,
        tagsList,
        categoriesList,
        handleDropdownChange,
        progressStatus,
        categoryId,
        handleProgressSubmission,
        progressLoading,
        suggestionReceivedFilter,
        suggestionFilterOptions,
        suggestionCategoryFilterOptions,
        handleSuggestionReceievedDropdownChange,
        handleSuggestionCategoryDropdownChange,
        suggestionPendingCount,
        suggestionComment,
        setSuggestionComment,
        comments,
        removeHtmlTags,
        isSameAsInitialData,
        handleClearFilter
    } = useSuggestion();

    const MIN_COMMENT_LENGTH = 10;
    const MAX_COMMENT_LENGTH = 200;

    const rawSuggestionComment = removeHtmlTags(suggestionComment).trim();

    const isSaveDisabled =
        isSameAsInitialData ||
        !progressStatus ||
        (progressStatus === SuggestionProgressId.Deferred && !rawSuggestionComment) ||
        (rawSuggestionComment.length > 0 && rawSuggestionComment.length < MIN_COMMENT_LENGTH) ||
        rawSuggestionComment.length > MAX_COMMENT_LENGTH;

    return (
        <PageContent>
            <ListHeader title="Suggestions" actionButtonLabel="Add Suggestion" actionButtonClick={addSuggestionPage} />

            <StyledPopup open={modalState} onCloseModal={closeModal}>
                <StyledModalHeader>
                    <StyledModalTitle textVariant="h2">Suggestion</StyledModalTitle>
                </StyledModalHeader>
                <StyledModalContent>
                    <StyledModalContentFlex>
                        <SuggestionLayer>
                            <StyledHeading textWeight="Medium">
                                Date: <StyledText>{suggestedDate}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Category: <StyledText>{categoryName}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Suggested By: <StyledText>{suggestedBy}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionDescriptionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Suggestion:
                            </StyledHeading>
                            <StyledHTMLText dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(suggestionText) }} />
                        </SuggestionDescriptionLayer>
                        {activeTab === 'receivedSuggestion' && (
                            <div>
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        gap: '1rem',
                                        width: '100%',
                                        marginTop: '1rem'
                                    }}
                                >
                                    <div style={{ width: '100%' }}>
                                        <StyledSingleSelect
                                            options={tagsList}
                                            variant="outlined"
                                            placeholder="Select Progress"
                                            size="M"
                                            label="Progress"
                                            value={progressStatus}
                                            isSearchable
                                            onChange={val => val && handleDropdownChange('progress', val)}
                                            data-testid="progress"
                                            selectMaxHeight="14rem"
                                            minWidth="100%"
                                        />
                                    </div>

                                    <div style={{ width: '100%' }}>
                                        <StyledSingleSelect
                                            options={categoriesList}
                                            variant="outlined"
                                            placeholder="Select Category"
                                            size="M"
                                            label="Category"
                                            value={categoryId}
                                            isSearchable
                                            onChange={val => val && handleDropdownChange('category', val)}
                                            data-testid="category"
                                            selectMaxHeight="14rem"
                                            minWidth="100%"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <CommentTextField
                                        label="Comment"
                                        variant="outlined"
                                        value={suggestionComment}
                                        minLength={MIN_COMMENT_LENGTH}
                                        maxLength={MAX_COMMENT_LENGTH}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSuggestionComment(e.target.value)}
                                        errorText={
                                            rawSuggestionComment.length > 0 && rawSuggestionComment.length < MIN_COMMENT_LENGTH
                                                ? `Comment must be between ${MIN_COMMENT_LENGTH} and ${MAX_COMMENT_LENGTH} characters.`
                                                : ''
                                        }
                                        multiline
                                        minRows={2}
                                    />
                                    <CommentLimitText>
                                        {rawSuggestionComment.length}/{MAX_COMMENT_LENGTH}
                                    </CommentLimitText>
                                </div>
                            </div>
                        )}
                        {comments.length > 0 && (
                            <SuggestionCommentsSection>
                                {comments.map(c => (
                                    <SuggestionCommentContainer key={c.id}>
                                        <StyledHeading textVariant="h4" textWeight="Medium">
                                            Comment: <StyledText>{c.date}</StyledText>
                                        </StyledHeading>
                                        <StyledHTMLText dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(c.comment) }} />
                                    </SuggestionCommentContainer>
                                ))}
                            </SuggestionCommentsSection>
                        )}
                    </StyledModalContentFlex>
                </StyledModalContent>
                {activeTab === 'receivedSuggestion' && (
                    <ActionsWrapper>
                        <StyledModalActions>
                            <Button isLoading={progressLoading} disabled={isSaveDisabled} onClick={handleProgressSubmission}>
                                Save
                            </Button>
                        </StyledModalActions>
                    </ActionsWrapper>
                )}
            </StyledPopup>
            <FilterWrapper>
                <StyledSingleSelect
                    options={suggestionFilterOptions}
                    variant="outlined"
                    placeholder="Select Progress"
                    data-testid="progressDropdown"
                    label={'Progress'}
                    onChange={e => handleSuggestionReceievedDropdownChange(e)}
                    value={suggestionReceivedFilter.progressId}
                    size="M"
                    minWidth="25rem"
                />
                <StyledSingleSelect
                    options={suggestionCategoryFilterOptions}
                    variant="outlined"
                    placeholder="Select Category"
                    data-testid="categoryDropdown"
                    label={'Category'}
                    onChange={e => handleSuggestionCategoryDropdownChange(e)}
                    value={suggestionReceivedFilter.categoryId}
                    size="M"
                    minWidth="25rem"
                />
                <ClearFilter
                    onClick={handleClearFilter}
                    disabled={Boolean(suggestionReceivedFilter.progressId == -99 && suggestionReceivedFilter.categoryId == -99)}
                />
            </FilterWrapper>
            <StyledTabs aria-label="Closed style tabs" tabSize="M" variant="outlined" onChange={id => handleTabChange(id)}>
                <Tabs.Tab
                    active={activeTab === 'submittedSuggestion'}
                    id="submittedSuggestion"
                    label="Submitted"
                    helperText="Submitted"
                    icon={CallMadeIcon}
                    data-testid="submitted"
                />
                <Tabs.Tab
                    active={activeTab === 'receivedSuggestion'}
                    id="receivedSuggestion"
                    label="Received"
                    helperText="Received"
                    icon={CallReceivedIcon}
                    count={suggestionPendingCount}
                    hide={!isReceivedAllowed}
                    data-testid="received"
                />
            </StyledTabs>
            <StyledTableWrapper>
                <CustomTable
                    data={suggestionsData}
                    tableKey={activeTab}
                    columns={activeTab === 'submittedSuggestion' ? SubmittedCols : ReceivedCols}
                    isLoading={suggestionsDataIsLoading}
                    activePage={page || 1}
                    count={totalSuggestionsDataCount || 0}
                    handlePageChange={handlePageChange}
                    setSortOrder={handleSortChange}
                />
            </StyledTableWrapper>
        </PageContent>
    );
};
