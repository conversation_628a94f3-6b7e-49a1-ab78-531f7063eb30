import { useCallback, useEffect, useMemo, useState } from 'react';
import { FILTER_OPTIONS, TUTORIALS_DATA } from './constants';
import { useAppDispatch, useAppSelector } from '@slice';
import { updateCategory, updateSearch, resetTutorialFilter } from '@slice/tutorialFilter';

const API_KEY = process.env.YOUTUBE_API_KEY;

async function getYoutubeViewCount(videoId: string) {
    const response = await fetch(`https://www.googleapis.com/youtube/v3/videos?part=statistics&id=${videoId}&key=${API_KEY}`);
    const data = await response.json();
    const viewCount = data.items[0]?.statistics?.viewCount;
    return viewCount;
}

export const useTutorial = (videoId?: string) => {
    const filterData = useAppSelector(state => state.tutorialFilter);
    const dispatch = useAppDispatch();
    const [viewCount, setViewCount] = useState<number>(0);
    const [openModal, setOpenModal] = useState<boolean>(false);

    const toggleModal = () => setOpenModal(prev => !prev);

    const onSearchClear = () => dispatch(updateSearch(''));

    const handleSearch = useCallback(
        (text: string) => {
            dispatch(updateSearch(text.toLowerCase()));
        },
        [dispatch]
    );

    const handleCategoryChange = useCallback(
        (category: string) => {
            dispatch(updateCategory(category));
        },
        [dispatch]
    );

    const handleClearFilter = useCallback(() => {
        dispatch(resetTutorialFilter());
    }, [dispatch]);

    useEffect(() => {
        if (!videoId) return;
        (async () => {
            try {
                const views = await getYoutubeViewCount(videoId);
                setViewCount(views);
            } catch (error) {
                console.error('Error fetching view count:', error);
            }
        })();
    }, [videoId]);

    const filteredTutorials = useMemo(() => {
        return TUTORIALS_DATA.filter(tutorial => {
            const matchesFilter =
                filterData?.category === 'all' ||
                tutorial.category.includes(FILTER_OPTIONS.find(opt => opt.value === filterData?.category)?.label || '');

            const matchesSearch =
                tutorial.cardLabel.toLowerCase().includes(filterData?.search) ||
                tutorial.cardDescription.toLowerCase().includes(filterData?.search) ||
                tutorial.cardTag.some(tag => tag.toLowerCase().includes(filterData?.search)) ||
                tutorial.category.some(cat => cat.toLowerCase().includes(filterData?.search));
            return matchesFilter && matchesSearch;
        });
    }, [filterData]);

    return {
        searchText: filterData?.search,
        viewCount,
        filter: filterData?.category,
        onSearchClear,
        openModal,
        toggleModal,
        filteredTutorials,
        handleSearch,
        handleCategoryChange,
        handleClearFilter
    };
};
