import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PerformanceReviewFiltersState } from './types';

export const initialState: PerformanceReviewFiltersState = {
    employees: [],
    filterRatingId: -1,
    reviewStatus: undefined
};

export const performanceReviewFiltersSlice = createSlice({
    name: 'performanceReviewFilter',
    initialState,
    reducers: {
        updatePerformanceReviewFilters(state, { payload }: PayloadAction<PerformanceReviewFiltersState>) {
            return {
                ...state,
                ...payload
            };
        },
        resetPerformanceReviewFilter() {
            return initialState;
        }
    }
});
