import { Toast } from '@medly-components/core';
import { SvgIcon } from '@medly-components/icons';
import styled from 'styled-components';

export const ToastContent = styled.div`
    display: flex;

    /* justify-content: space-between; */
    align-items: center;
    width: 100%;
    background-color: transparent !important;

    ${SvgIcon} {
        transition: all 100ms ease-out;
        background-color: transparent;
        * {
            fill: ${({ theme }) => theme.colors.black};
        }
        &:hover {
            background-color: ${({ theme }) => theme.colors.grey[100]};
        }
    }
`;

export const DraftBannerToast = styled(Toast)`
    position: fixed;
    top: 7.6rem;
    left: 55%;
    transform: translateX(-50%);
    z-index: 10;
    margin-bottom: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 0.4rem solid orange;
    width: 58rem;
    max-width: 90vw;

    div:first-child {
        align-items: center;
        background-color: rgba(255, 165, 0, 0.1);
    }

    div:nth-child(2) {
        justify-content: center;
        align-items: center;
        padding: 0.8rem 1.6rem;
        padding-right: 0;
    }

    div:nth-child(3) {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    svg:nth-child(1) {
        * {
            fill: orange;
        }
    }

    svg[title='toast-close-icon'] {
        margin-left: 1rem;
        cursor: pointer;
    }
`;
