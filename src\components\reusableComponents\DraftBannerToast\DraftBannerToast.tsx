import { Text } from '@medly-components/core';
import { ClearIcon } from '@medly-components/icons';
import { FC, useState } from 'react';
import { DraftBannerToast, ToastContent } from './DraftBannerToast.styled';

interface DraftBannerToastProps {
    message: string;
    onClose?: () => void;
}

export const DraftBannerToastComponent: FC<DraftBannerToastProps> = ({ message, onClose }) => {
    const [isVisible, setIsVisible] = useState(true);

    const handleToastClick = (e: React.MouseEvent) => {
        if ((e.target as HTMLElement).id === 'draft-toast-close') {
            setIsVisible(false);
            onClose?.();
        }
    };

    if (!isVisible) {
        return null;
    }

    return (
        <DraftBannerToast
            id={2}
            variant="info"
            hideCloseIcon={true}
            onClick={handleToastClick}
            message={
                <ToastContent>
                    <Text textVariant="body2" textWeight="Medium">
                        {message}
                    </Text>
                    <ClearIcon id="draft-toast-close" title="toast-close-icon" size="XS" variant="solid" />
                </ToastContent>
            }
        />
    );
};
