import { StyledModalContent, StyledModalHeader, StyledModalTitle } from '@common';
import { format } from 'date-fns';
import { Feedback } from './types';
import {
    StyledPopup,
    StyledModalContentFlex,
    FeedbackLayer,
    StyledHeading,
    StyledText,
    FeedbackDescriptionLayer
} from './AppFeedbackModal.styled';

interface AppFeedbackModalProps {
    isOpen: boolean;
    onClose: () => void;
    feedbackData: Feedback | null;
}

export const AppFeedbackModal = ({ isOpen, onClose, feedbackData }: AppFeedbackModalProps) => {
    if (!feedbackData) return null;

    // Define the fields to display with their labels and values
    const feedbackFields = [
        {
            label: 'Date',
            value: format(new Date(feedbackData.createdAt), 'dd/MM/yyyy'),
            type: 'inline' as const
        },
        {
            label: 'Organization ID',
            value: feedbackData.organisationId,
            type: 'inline' as const
        },
        {
            label: 'Organization Name',
            value: feedbackData.organisationName,
            type: 'inline' as const
        },
        {
            label: 'Employee Name',
            value: feedbackData.feedbackFromName,
            type: 'inline' as const
        },
        // {
        //     label: 'Employee ID',
        //     value: feedbackData.feedbackFromId,
        //     type: 'inline' as const
        // },
        {
            label: 'Feedback',
            value: feedbackData.feedback,
            type: 'description' as const
        }
    ];

    return (
        <StyledPopup open={isOpen} onCloseModal={onClose}>
            <StyledModalHeader>
                <StyledModalTitle textVariant="h2">App Feedback Details</StyledModalTitle>
            </StyledModalHeader>
            <StyledModalContent>
                <StyledModalContentFlex>
                    {feedbackFields.map((field, index) =>
                        field.type === 'inline' ? (
                            <FeedbackLayer key={index}>
                                <StyledHeading textWeight="Medium">
                                    {field.label}: <StyledText>{field.value}</StyledText>
                                </StyledHeading>
                            </FeedbackLayer>
                        ) : (
                            <FeedbackDescriptionLayer key={index}>
                                <StyledHeading textWeight="Medium">{field.label}:</StyledHeading>
                                <StyledText>{field.value}</StyledText>
                            </FeedbackDescriptionLayer>
                        )
                    )}
                </StyledModalContentFlex>
            </StyledModalContent>
        </StyledPopup>
    );
};
