import { StyledSearchBox as StyledSearchBoxParent } from '@common';
import styled from 'styled-components';

export const StyledWrapper = styled.div`
    display: flex;
    max-width: 84rem;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
`;

export const FilterContainer = styled.div`
    display: flex;
    flex-direction: row;
    column-gap: 1rem;
    align-items: center;

    @media (max-width: 1815px) {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: flex-end;
    }
`;

export const StyledSearchBox = styled(StyledSearchBoxParent)`
    min-width: 35rem;
    max-width: unset;
    max-height: 45px;

    input {
        color: #333;
    }

    input::placeholder {
        color: #666;
    }
`;

export const FilterWrapperStyle = styled.div`
    display: flex;
    gap: 1rem;
    align-items: center;
`;

export const ClearFilterButton = styled.button`
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    border-radius: 0.4rem;
    margin-bottom: 0.6rem;

    svg {
        fill: #2b64e3;
        transition: fill 0.2s ease-in-out;
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.3;

        svg {
            fill: #444444ff;
        }
    }

    &:focus-visible {
        outline: 2px solid #878787;
        outline-offset: 2px;
    }
`;
