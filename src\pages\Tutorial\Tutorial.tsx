import { StyledEmptyText, StyledSearchBox, StyledSingleSelect } from '@common';
import { PageContent } from '@components';
import TutorialCardModal from '@components/TutorialCardModal/TutorialCardModal';
import ListHeader from '@components/reusableComponents/ListHeader';
import { Text } from '@medly-components/core';
import { CardsWrapper, FlexBoxRow } from './Tutorial.styled';
import { FILTER_OPTIONS } from './constants';
import { useTutorial } from './useTutorial';
import ClearFilter from '@components/reusableComponents/ClearFilter/ClearFilter';

export const Tutorial = () => {
    const { searchText, filter, filteredTutorials, handleSearch, onSearchClear, handleCategoryChange, handleClearFilter } = useTutorial();

    return (
        <PageContent>
            <ListHeader
                title={
                    <Text textVariant="h3" style={{ fontSize: '20px' }} textWeight="Medium">
                        Tutorial Videos
                    </Text>
                }
            />

            <FlexBoxRow>
                <StyledSearchBox
                    placeholder="Search by title, description, or tag"
                    onInputChange={handleSearch}
                    onClear={onSearchClear}
                    value={searchText}
                    size="M"
                    className="h-40"
                    minWidth="30rem"
                    data-testid="search-tutorial"
                />

                <StyledSingleSelect
                    options={FILTER_OPTIONS}
                    variant="outlined"
                    size="S"
                    placeholder="Select Category"
                    label="Select Category"
                    className="h-40"
                    value={filter}
                    isSearchable
                    minWidth="30rem"
                    onChange={handleCategoryChange}
                    data-testid="filter"
                />
                <ClearFilter onClick={handleClearFilter} disabled={Boolean(searchText === '' && filter === 'all')} />
            </FlexBoxRow>

            <CardsWrapper>
                {filteredTutorials.length > 0 ? (
                    filteredTutorials.map((tutorial, index) => <TutorialCardModal key={index} video={tutorial} />)
                ) : (
                    <StyledEmptyText style={{ marginTop: '2rem' }}>More tutorial videos coming soon...</StyledEmptyText>
                )}
            </CardsWrapper>
        </PageContent>
    );
};
