import { fireEvent, screen } from '@testing-library/react';
import { RTKRender } from '@utils/test-utils';
import { DraftBannerToastComponent } from './DraftBannerToast';

describe('DraftBannerToastComponent', () => {
    const defaultProps = {
        message: 'Test draft banner message'
    };

    it('should render the component with message', () => {
        RTKRender(<DraftBannerToastComponent {...defaultProps} />);
        expect(screen.getByText('Test draft banner message')).toBeInTheDocument();
    });

    it('should render the close icon', () => {
        RTKRender(<DraftBannerToastComponent {...defaultProps} />);
        const closeIcon = screen.getByTitle('toast-close-icon');
        expect(closeIcon).toBeInTheDocument();
    });

    it('should hide the component when close icon is clicked', () => {
        RTKRender(<DraftBannerToastComponent {...defaultProps} />);

        // Initially the message should be visible
        expect(screen.getByText('Test draft banner message')).toBeInTheDocument();

        // Click the close icon
        const closeIcon = screen.getByTitle('close-icon');
        fireEvent.click(closeIcon);

        // The message should no longer be visible
        expect(screen.queryByText('Test draft banner message')).not.toBeInTheDocument();
    });

    it('should call onClose callback when close icon is clicked', () => {
        const mockOnClose = jest.fn();
        RTKRender(<DraftBannerToastComponent {...defaultProps} onClose={mockOnClose} />);

        // Click the close icon
        const closeIcon = screen.getByTitle('close-icon');
        fireEvent.click(closeIcon);

        // onClose should have been called
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should not call onClose when clicking on the message text', () => {
        const mockOnClose = jest.fn();
        RTKRender(<DraftBannerToastComponent {...defaultProps} onClose={mockOnClose} />);

        // Click on the message text
        const messageText = screen.getByText('Test draft banner message');
        fireEvent.click(messageText);

        // onClose should not have been called
        expect(mockOnClose).not.toHaveBeenCalled();

        // The message should still be visible
        expect(screen.getByText('Test draft banner message')).toBeInTheDocument();
    });

    it('should work without onClose callback', () => {
        RTKRender(<DraftBannerToastComponent {...defaultProps} />);

        // Click the close icon - should not throw error
        const closeIcon = screen.getByTitle('close-icon');
        expect(() => fireEvent.click(closeIcon)).not.toThrow();

        // The message should no longer be visible
        expect(screen.queryByText('Test draft banner message')).not.toBeInTheDocument();
    });

    it('should take snapshot', () => {
        const { container } = RTKRender(<DraftBannerToastComponent {...defaultProps} />);
        expect(container).toMatchSnapshot();
    });
});
