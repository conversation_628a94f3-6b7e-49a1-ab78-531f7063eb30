import { routeConstants } from '@constants';
import apiUrls from '@constants/apiUrls';
import { Option } from '@medly-components/core/dist/es/components/SingleSelect/types';
import { debounce } from '@medly-components/utils';
import { useAppDispatch, useAppSelector } from '@slice';
import { updateKPIFilter, updateSearch, resetKPIFilter } from '@slice/kpiFilter';
import { useGetKpisQuery, useGetReviewCycleDataMutation, useGetTeamsQuery, useGetDesignationsQuery } from '@slice/services';
import { downloadCSV } from '@utils/downloadCSV';
import { downloadPDF } from '@utils/downloadPDF';
import { useCallback, useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { kpiStatusOptions } from '../../constants/data';
import { KpiTypes } from './types';
import { useDepartmentDropdownData } from '@common/hooks/useDepartmentDropdownData';
import { useKRADropdownData } from '@common/hooks/useKRADropdownData';
import { Designation, Team } from './types';

export const useKpiManagement = () => {
    const userDetails = useAppSelector(state => state.user),
        { ActivePage } = useParams(),
        { modulePermission } = useAppSelector(state => state.user);
    const filterData = useAppSelector(state => state.kpiFilter);
    const { search } = filterData;
    const module = modulePermission?.find(module => module.moduleName === 'KPIs');

    const dispatch = useAppDispatch();
    const [sampleKpiModal, setSampleKpiModal] = useState(false);
    const [page, setPage] = useState<number>();
    const [kpis, setKpis] = useState<KpiTypes>([]);
    const [searchText, setSearchText] = useState(search || '');
    const [isPdfExporting, setIsPdfExporting] = useState(false);
    const navigateTo = useNavigate();
    const navigate = useNavigate();
    const [getActiveReviewCycle, { data: activeReviewCycleData, isSuccess: hasFetchedActiveCycle }] = useGetReviewCycleDataMutation();
    const { departmentList } = useDepartmentDropdownData(),
        { data: teams, isSuccess: isTeamsReceived } = useGetTeamsQuery(
            {
                path: '',
                params: [
                    { name: 'organisationId', value: userDetails.organisationId },
                    ...(filterData.selectedDepartments.length ? [{ name: 'departmentId', value: filterData.selectedDepartments }] : [])
                ]
            },
            { refetchOnMountOrArgChange: true }
        ),
        { data: designations, isSuccess: isDesignationsReceived } = useGetDesignationsQuery(
            {
                path: '',
                params: [
                    { name: 'organisationId', value: userDetails.organisationId },
                    ...(filterData.selectedTeams.length ? [{ name: 'teamId', value: filterData.selectedTeams }] : [])
                ]
            },
            { refetchOnMountOrArgChange: true }
        );
    const { krasList } = useKRADropdownData();

    const [enableAddKpi, setEnableAddKpi] = useState(false);
    const {
        data,
        isSuccess,
        isFetching: isLoading,
        error
    } = useGetKpisQuery(
        {
            path: '',
            params: [
                { name: 'page', value: ActivePage ?? page },
                { name: 'limit', value: 10 },
                { name: 'organisationId', value: userDetails.organisationId },
                { name: 'searchText', value: search },
                { name: 'kraId', value: filterData.selectedKRAs.length ? filterData.selectedKRAs : -99 },
                { name: 'teamId', value: filterData.selectedTeams.length ? filterData.selectedTeams : -99 },
                { name: 'designationId', value: filterData.selectedDesignations.join(',') || -99 },
                { name: 'departmentId', value: filterData.selectedDepartments.join(',') || -99 },
                { name: 'status', value: filterData.selectedStatus.length ? filterData.selectedStatus : 'true,false' }
            ]
        },
        { refetchOnMountOrArgChange: true }
    );

    const DownloadKpiTemplate = (path: string) => {
        downloadCSV(`${apiUrls.kpi}/${path}`, path);
    };

    const onSearchClear = () => {
        setSearchText('');
        dispatch(updateSearch(''));
        navigate(`${routeConstants.kpiManagement}/${1}`);
    };

    const updateSearchInStore = useCallback(
        (value: string) => {
            dispatch(updateSearch(value));
            navigate(`${routeConstants.kpiManagement}/${1}`);
        },
        [dispatch, navigate]
    );

    const debouncedUpdateStore = useCallback(
        debounce((value: string) => {
            updateSearchInStore(value);
        }, 500),
        [updateSearchInStore]
    );

    const onSearch = useCallback(
        (value: string) => {
            setSearchText(value);
            dispatch(updateKPIFilter({ ...filterData, search: value }));
            navigate(`${routeConstants.kpiManagement}/${1}`);
        },
        [dispatch, filterData, navigate]
    );

    const debouncedOnChange = useCallback(
        debounce(value => {
            onSearch(value);
        }, 500),
        [onSearch]
    );

    const onSearchChange = useCallback(
        (val: string) => {
            setSearchText(val);
            debouncedUpdateStore(val);
        },
        [debouncedUpdateStore]
    );

    const openAddNewKPI = () => {
        navigate(`${routeConstants.kpiManagement}/${ActivePage}/add-KPI`, { state: { action: 'add' } });
    };

    const teamsList = useMemo(() => {
        const filteredList = isTeamsReceived ? teams?.teams?.filter((team: Team) => team.teamStatus === true) : [];
        const list =
            isTeamsReceived && filteredList?.length
                ? filteredList.map((team: Team) => {
                      // temporarily using like this, might be reverted back
                      // return { value: team.id, label: `${team.teamName} (${team.departmentName ?? '-'})` };
                      return { value: team.id, label: `${team.teamName}` };
                  })
                : [];
        list.sort((a: Option, b: Option) => a.label.localeCompare(b.label));
        return list;
    }, [isTeamsReceived, teams]);

    const designationsList = useMemo(() => {
        if (isDesignationsReceived && designations.designations && designations.designations.length) {
            const filteredList = designations?.designations
                .filter((designation: Designation) => designation.status === true)
                .map((d: Designation) => ({ label: `${d.designationName} (${d.teamName})`, value: d.id }));

            return filteredList.sort((a: Option, b: Option) => a.label.localeCompare(b.label));
        } else {
            return [];
        }
    }, [designations, isDesignationsReceived]);

    type dropDown = 'selectedDepartments' | 'selectedTeams' | 'selectedKRAs' | 'selectedDesignations' | 'selectedStatus';

    const handleDropdownChange = useCallback(
        (name: dropDown, value: any) => {
            let updatedFilter = { ...filterData };
            if (name === 'selectedDepartments' && value.length === 0) {
                updatedFilter = {
                    ...filterData,
                    selectedDepartments: [],
                    selectedTeams: [],
                    selectedDesignations: []
                };
            } else if (name === 'selectedTeams' && value.length === 0) {
                updatedFilter = {
                    ...filterData,
                    selectedTeams: [],
                    selectedDesignations: []
                };
            }

            dispatch(updateKPIFilter({ ...updatedFilter, [name]: value, moduleName: `/KPIs` }));
            navigateTo(`${routeConstants.kpiManagement}/1`);
        },
        [dispatch, filterData, navigateTo]
    );

    const handleBulkImportClick = () => {
        navigateTo(`${routeConstants.kpiManagement}/${ActivePage}/bulk-import`, { state: { action: 'add', bulkImportType: 'kpis' } });
    };

    const handlePageChange = (page: number) => {
        setPage(page);
        navigate(`${routeConstants.kpiManagement}/${page}`);
    };

    const handlePDFExport = () => {
        const departmentId = filterData.selectedDepartments.join(',') || '-99';
        const teamId = filterData.selectedTeams.join(',') || '-99';
        const designationId = filterData.selectedDesignations.join(',') || '-99';
        const status = filterData.selectedStatus;
        const kraId = filterData.selectedKRAs.join(',') || '-99';

        const url = `${apiUrls.kpi}/download?organisationId=${userDetails.organisationId}&departmentId=${departmentId}&teamId=${teamId}&designationId=${designationId}&status=${status}&kraId=${kraId}`;

        downloadPDF(url, 'kpi-export', setIsPdfExporting);
    };

    const handleClearFilter = useCallback(() => {
        dispatch(resetKPIFilter());
        setSearchText('');
        navigateTo(`${routeConstants.kpiManagement}/${1}`);
    }, [dispatch, navigateTo]);

    const isClearFilterDisabled = useMemo(() => {
        return (
            searchText === '' &&
            (!filterData.selectedKRAs || filterData.selectedKRAs.length === 0) &&
            filterData.selectedStatus == 'true,false' &&
            (!filterData.selectedDepartments || filterData.selectedDepartments.length === 0) &&
            (!filterData.selectedTeams || filterData.selectedTeams.length === 0) &&
            (!filterData.selectedDesignations || filterData.selectedDesignations.length === 0)
        );
    }, [
        searchText,
        filterData.selectedKRAs,
        filterData.selectedStatus,
        filterData.selectedDepartments,
        filterData.selectedTeams,
        filterData.selectedDesignations
    ]);

    useEffect(() => {
        if (data && isSuccess) {
            setKpis(data.kpis);
        }
    }, [data, isSuccess]);

    useEffect(() => {
        if (error) {
            setKpis([]);
        }
    }, [error]);

    const openSampleKpiModal = () => {
        setSampleKpiModal(prev => !prev);
    };

    useEffect(() => {
        getActiveReviewCycle({
            path: 'active',
            params: [{ name: 'organisationId', value: userDetails.organisationId }]
        });
    }, [getActiveReviewCycle, userDetails.organisationId]);

    useEffect(() => {
        if (
            activeReviewCycleData &&
            new Date(activeReviewCycleData.selfReviewStartDate) > new Date() &&
            new Date(activeReviewCycleData.managerReviewStartDate) > new Date()
        ) {
            setEnableAddKpi(true);
        }
        if (!activeReviewCycleData?.selfReviewStartDate && hasFetchedActiveCycle) {
            setEnableAddKpi(true);
        }
    }, [activeReviewCycleData, hasFetchedActiveCycle]);

    const isExportKpiEnabled = useMemo(() => {
        return (
            filterData?.selectedDepartments.length === 1 &&
            filterData?.selectedTeams.length === 1 &&
            filterData?.selectedDesignations.length === 1 &&
            kpis?.length > 0
        );
    }, [filterData?.selectedDepartments, filterData?.selectedTeams, filterData?.selectedDesignations, kpis]);

    return {
        openAddNewKPI,
        isLoading,
        kpis,
        debouncedOnChange,
        onSearch,
        onSearchClear,
        ActivePage,
        handlePageChange,
        searchText,
        totalItems: kpis?.length ? data?.totalKPIs : 0,
        openSampleKpiModal,
        sampleKpiModal,
        enableAddKpi,
        isExportKpiEnabled,
        DownloadKpiTemplate,
        handleDropdownChange,
        handleBulkImportClick,
        handlePDFExport,
        isPdfExporting,
        onSearchChange,
        search,
        kpiStatusOptions,
        krasList,
        departmentList,
        teamsList,
        designationsList,
        filterData,
        module,
        handleClearFilter,
        isClearFilterDisabled
    };
};
